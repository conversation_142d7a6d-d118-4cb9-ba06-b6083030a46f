package vn.ghtk.oms.partner.logistics.selection;

import vn.ghtk.oms.ordermgt.domain.LogisticsPartner;
import vn.ghtk.oms.ordermgt.domain.Order;
import vn.ghtk.oms.partner.logistics.operations.LogisticsPartnerSelector;

public class SimpleLogisticsPartnerSelector implements LogisticsPartnerSelector {
    @Override
    public LogisticsPartner selectSuitablePartnerFor(Order order) {
        return new LogisticsPartner("ghtk");
    }
}
