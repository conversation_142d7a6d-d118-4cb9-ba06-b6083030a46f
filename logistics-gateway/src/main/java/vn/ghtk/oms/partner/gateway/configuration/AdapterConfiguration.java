package vn.ghtk.oms.partner.gateway.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreationCmd;
import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreator;
import vn.ghtk.oms.partner.gateway.domain.Shipment;

@Configuration
public class AdapterConfiguration {

    @Bean
    public ShipmentCreator getShipmentCreator() {
        return new ShipmentCreator() {
            @Override
            public Shipment execute(ShipmentCreationCmd cmd) {
                return null;
            }
        };
    }
}
