package vn.ghtk.oms.partner.gateway.adapter.facade;


import org.springframework.stereotype.Service;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.LogisticsPartnerView;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.OrderView;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.ShipmentView;
import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreationCmd;
import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreator;
import vn.ghtk.oms.partner.gateway.domain.Shipment;

@Service
public class ShipmentCreationFacade {

    private final ShipmentCreator shipmentCreator;

    public ShipmentCreationFacade(ShipmentCreator shipmentCreator) {
        this.shipmentCreator = shipmentCreator;
    }

    public ShipmentView createShipment(OrderView orderView, LogisticsPartnerView partnerView) {
        ShipmentCreationCmd cmd = this.createCmd(orderView, partnerView);
        Shipment shipment = this.shipmentCreator.execute(cmd);
        return ShipmentView.of(shipment);
    }

    private ShipmentCreationCmd createCmd(OrderView orderView, LogisticsPartnerView partnerView) {
        // TODO
        return null;
    }
}
