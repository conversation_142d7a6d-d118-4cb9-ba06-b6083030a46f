package vn.ghtk.oms.partner.gateway.application.usecase;

import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreationCmd;
import vn.ghtk.oms.partner.gateway.application.inbound.ShipmentCreator;
import vn.ghtk.oms.partner.gateway.domain.Shipment;

public class ShipmentCreatorUc implements ShipmentCreator {
    @Override
    public Shipment execute(ShipmentCreationCmd cmd) {

        return null;
    }
}
