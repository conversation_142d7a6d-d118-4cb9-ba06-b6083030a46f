package vn.ghtk.oms.partner.gateway.adapter.inbound.restful;


import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.ghtk.oms.partner.gateway.adapter.facade.ShipmentCreationFacade;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.LogisticsPartnerView;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.OrderView;
import vn.ghtk.oms.partner.gateway.adapter.inbound.dto.ShipmentView;

@RestController("/api/v1/shipments")
public class ShipmentCreationController {

    private ShipmentCreationFacade facade;

    @PostMapping
    public ResponseEntity<String> createShipment(OrderView orderView, LogisticsPartnerView partnerView) {
        ShipmentView shipment = this.facade.createShipment(orderView, partnerView);
        return ResponseEntity.ok().body(shipment.toString());
    }
}
