# GHTK Shipment Order API Documentation

**Version:** 1.0  
**Base URL:** `http://localhost:8080/api/v1`  
**Description:** API để tạo đơn hàng gửi đến GHTK

## Authentication

API sử dụng API Key authentication thông qua header `Token`.

```
Token: YOUR_API_KEY
```

## Endpoints

### 1. Tạo đơn hàng giao vận

**POST** `/shipments`

Tạo một đơn hàng giao vận mới trong hệ thống GHTK.

#### Request Body

```json
{
  "products": [
    {
      "name": "string (required)",
      "price": "integer",
      "weight": "number (required)",
      "quantity": "integer",
      "code": "string"
    }
  ],
  "orderView": {
    "id": "string (required)",
    "pickup": {
      "name": "string (required)",
      "cod_amount": "integer",
      "address_id": "string",
      "address": "string (required)",
      "province": "string (required)",
      "district": "string (required)",
      "ward": "string",
      "street": "string",
      "tel": "string (required)",
      "email": "string",
      "work_shift": "integer (1|2|3)",
      "date": "string (YYYY/MM/DD)",
      "method": "string (cod|post)"
    },
    "delivery": {
      "name": "string",
      "address": "string",
      "province": "string",
      "district": "string",
      "ward": "string",
      "street": "string",
      "hamlet": "string",
      "tel": "string",
      "email": "string",
      "work_shift": "integer (1|2|3)",
      "date": "string (YYYY/MM/DD)",
      "method": "string (xteam)"
    },
    "note": "string (max 120 chars)",
    "use_return_address": "integer (0|1)",
    "return": {
      "name": "string",
      "address": "string",
      "province": "string",
      "district": "string",
      "ward": "string",
      "street": "string",
      "tel": "string",
      "email": "string"
    },
    "is_freeship": "integer (0|1)",
    "weight_option": "string (gram|kilogram)",
    "total_weight": "number",
    "label_id": "string",
    "expired": "string (datetime)",
    "value": "integer (required)",
    "opm": "integer",
    "actual_transfer_method": "string (fly|road)",
    "transport": "string (fly|road)",
    "tags": ["string"],
    "sub_tags": ["string"]
  }
}
```

#### Field Descriptions

**Products:**
- `name`: Tên hàng hóa
- `price`: Giá trị hàng hóa
- `weight`: Khối lượng hàng hóa (kg)
- `quantity`: Số lượng hàng hóa
- `code`: Mã sản phẩm

**Order - Pickup:**
- `name`: Tên người liên hệ lấy hàng hóa
- `cod_amount`: Số tiền COD (VNĐ). Nếu bằng 0 thì không thu tiền
- `address_id`: ID địa điểm lấy hàng từ trang quản lý của khách hàng (ưu tiên nếu có)
- `address`: Địa chỉ ngắn gọn lấy hàng
- `province`: Tỉnh/thành phố lấy hàng
- `district`: Quận/huyện lấy hàng
- `ward`: Phường/xã lấy hàng
- `street`: Tên đường lấy hàng
- `tel`: Số điện thoại nơi lấy hàng
- `work_shift`: Ca lấy hàng (1 = sáng, 2 = chiều, 3 = tối)
- `date`: Ngày lấy hàng (YYYY/MM/DD, yêu cầu cấu hình riêng)
- `method`: Hình thức lấy hàng (COD tới lấy, shop gửi tại bưu cục)

**Order - Delivery:**
- `name`: Tên người nhận hàng
- `address`: Địa chỉ chi tiết người nhận hàng
- `province`: Tỉnh/thành phố người nhận
- `district`: Quận/huyện người nhận
- `ward`: Phường/xã người nhận (bắt buộc khi không có street)
- `street`: Tên đường người nhận (bắt buộc khi không có ward)
- `hamlet`: Thôn/ấp/xóm/tổ... người nhận. Nếu không có, dùng "Khác"
- `tel`: Số điện thoại người nhận
- `work_shift`: Ca giao hàng (1 = sáng, 2 = chiều, 3 = tối)
- `date`: Ngày giao hàng (YYYY/MM/DD, yêu cầu cấu hình riêng)
- `method`: Giao hàng bằng phương thức xfast (xteam)

**Other Order Fields:**
- `note`: Ghi chú đơn hàng (tối đa 120 ký tự)
- `use_return_address`: 1 = sử dụng địa chỉ trả hàng khác; 0 = giống địa chỉ lấy hàng
- `is_freeship`: 1 = người nhận không chịu phí ship (COD chỉ thu cod_amount)
- `weight_option`: Đơn vị khối lượng (gram hoặc kilogram), mặc định: kilogram
- `total_weight`: Tổng khối lượng đơn hàng (sẽ tính theo products.weight nếu không truyền)
- `value`: Giá trị đóng khai giá, là căn cứ để tính phí khai giá và bồi thường khi có sự cố
- `opm`: 1 = đơn chỉ thu tiền, 0 = bình thường (mặc định)
- `actual_transfer_method`: Hình thức vận chuyển thực tế (mặc định fly)
- `transport`: Phương thức vận chuyển
- `tags`: Danh sách nhãn gắn vào đơn hàng
- `sub_tags`: Chi tiết nhãn đơn hàng (bắt buộc khi đơn là hàng cây cối)

#### Example Request

```json
{
  "products": [
    {
      "name": "Áo thun",
      "price": 200000,
      "weight": 0.3,
      "quantity": 2,
      "product_code": "TSHIRT001"
    }
  ],
  "orderView": {
    "id": "ORDER12345",
    "pickup": {
      "name": "Nguyễn Văn A",
      "address": "123 Đường ABC",
      "province": "Hà Nội",
      "district": "Quận Cầu Giấy",
      "tel": "0901234567"
    },
    "delivery": {
      "name": "Trần Thị B",
      "address": "456 Đường XYZ",
      "province": "Hồ Chí Minh",
      "district": "Quận 1",
      "ward": "Phường Bến Nghé",
      "hamlet": "Khác",
      "tel": "0912345678"
    },
    "value": 500000
  }
}
```

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "string",
  "orderView": {
    "partner_id": "string",
    "label": "string",
    "area": "string",
    "fee": "string",
    "insurance_fee": "string",
    "tracking_id": "integer",
    "estimated_pickup_time": "string",
    "estimated_delivery_time": "string",
    "products": [],
    "status_id": "integer"
  }
}
```

**Error Responses:**
- `400`: Dữ liệu không hợp lệ
- `401`: Không xác thực
- `500`: Lỗi máy chủ nội bộ

---

### 2. Tính phí đơn hàng

**POST** `/shipments/fee-estimate`

API dùng để tính toán phí ship và các phụ phí liên quan đến đơn hàng dựa trên các thông tin như địa chỉ lấy hàng, địa chỉ giao hàng, cân nặng, dịch vụ đơn hàng đã chọn.

#### Request Body

```json
{
  "pickup": {
    "address_id": "string",
    "address": "string",
    "province": "string",
    "district": "string",
    "ward": "string",
    "street": "string"
  },
  "delivery": {
    "address": "string",
    "province": "string",
    "district": "string",
    "ward": "string",
    "street": "string",
    "method": "string (xteam|none)"
  },
  "weight": "integer (minimum: 1)",
  "value": "integer (minimum: 0)",
  "transport": "string (fly|road)",
  "tags": ["string"]
}
```

#### Field Descriptions

**Pickup:**
- `address_id`: ID địa điểm lấy hàng của shop (ưu tiên nếu có)
- `address`: Địa chỉ ngắn gọn lấy hàng
- `province`: Tỉnh/thành phố nơi lấy hàng hóa
- `district`: Quận/huyện nơi lấy hàng hóa
- `ward`: Phường/xã nơi lấy hàng (tùy chọn)
- `street`: Tên đường/phố nơi lấy hàng (tùy chọn)

**Delivery:**
- `address`: Địa chỉ chi tiết của người nhận hàng
- `province`: Tỉnh/thành phố của người nhận hàng hóa
- `district`: Quận/huyện của người nhận hàng hóa
- `ward`: Phường/xã của người nhận hàng hóa (tùy chọn)
- `street`: Tên đường/phố của người nhận hàng hóa (tùy chọn)
- `method`: Phương thức giao hàng. `xteam` nếu sử dụng dịch vụ xfast

**Other Fields:**
- `weight`: Cân nặng gói hàng (gram)
- `value`: Giá trị đơn hàng để tính phí khai giá (VNĐ)
- `transport`: Phương thức vận chuyển (`road` - bộ, `fly` - bay)
- `tags`: Gắn nhãn cho đơn hàng (dạng mảng)

#### Example Request

```json
{
  "pickup": {
    "address_id": "addr_123",
    "address": "123 Lê Lợi",
    "province": "Hà Nội",
    "district": "Hoàn Kiếm",
    "ward": "Hàng Bài",
    "street": "Lê Lợi"
  },
  "delivery": {
    "address": "456 Nguyễn Huệ",
    "province": "Hồ Chí Minh",
    "district": "Quận 1",
    "ward": "Bến Nghé",
    "street": "Nguyễn Huệ",
    "method": "xteam"
  },
  "weight": 1500,
  "value": 500000,
  "transport": "road",
  "tags": ["fragile", "high-priority"]
}
```

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "string",
  "fee": {
    "name": "string",
    "fee": "integer",
    "insurance_fee": "integer",
    "delivery_type": "string",
    "a": "integer",
    "dt": "string",
    "extFees": [
      {
        "display": "string",
        "title": "string",
        "amount": "integer",
        "type": "string"
      }
    ],
    "delivery": "boolean"
  }
}
```

**Error Responses:**
- `400`: Tham số truyền vào không hợp lệ
- `401`: Không có quyền truy cập
- `500`: Lỗi hệ thống

---

### 3. Lấy trạng thái đơn hàng

**GET** `/shipments/{tracking_order}`

API dùng để lấy trạng thái hiện tại của một mã vận đơn GHTK. Dựa trên `orderView.id` hoặc mã vận đơn GHTK (`label_id`) để truy vấn trạng thái.

#### Parameters

- `tracking_order` (path, required): Mã vận đơn GHTK hoặc mã đơn hàng đối tác

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "",
  "orderView": {
    "label_id": "string",
    "partner_id": "string",
    "status": "string",
    "status_text": "string",
    "created": "datetime",
    "modified": "datetime",
    "message": "string",
    "pickup_date": "date",
    "delivery_date": "date",
    "customer_fullname": "string",
    "customer_tel": "string",
    "address": "string",
    "storage_day": "integer",
    "shipping_fee": "integer",
    "insurance": "integer",
    "value": "integer",
    "weight": "integer",
    "cod_amount": "integer",
    "is_freeship": "boolean"
  }
}
```

#### Field Descriptions

- `label_id`: Mã đơn hàng của hệ thống GHTK
- `partner_id`: Mã đơn hàng của đối tác
- `status`: Mã trạng thái đơn hàng
- `status_text`: Trạng thái đơn hàng
- `message`: Ghi chú đơn hàng
- `pickup_date`: Ngày hẹn lấy hàng của đơn hàng nếu có, nếu đơn hàng đã được lấy thành công thì là ngày lấy hàng
- `delivery_date`: Ngày hẹn giao đơn hàng nếu có, nếu đơn hàng đã được giao hàng thì là ngày giao hàng thành công
- `customer_fullname`: Họ tên người nhận hàng
- `customer_tel`: Số điện thoại người nhận hàng
- `address`: Địa chỉ người nhận hàng
- `storage_day`: Số ngày giữ đơn hàng tại kho GHTK trước khi trả hàng
- `shipping_fee`: Phí giao hàng
- `insurance`: Phí khai giá
- `value`: Giá trị đóng khai giá - căn cứ để bồi thường cho người gửi khi có sự cố xảy ra
- `weight`: Đơn vị tính gram
- `cod_amount`: Số tiền CoD
- `is_freeship`: Miễn phí ship hay không

**Error Responses:**
- `404`: Không tìm thấy đơn hàng
- `401`: Không có quyền truy cập
- `500`: Lỗi máy chủ

---

### 4. Huỷ đơn hàng

**DELETE** `/shipments/{tracking_order}`

API dùng để huỷ 1 đơn hàng đã đẩy lên hệ thống GHTK.

#### Usage Notes

**TH dùng mã đối tác:**
`TRACKING_ORDER = partner_id:{PARTNER_CODE}`

**Các trạng thái có thể huỷ:**
- Trạng thái chưa tiếp nhận (1)
- Trạng thái đã tiếp nhận (2)
- Trạng thái đang lấy hàng (12)

Sau trạng thái đã lấy hàng, đơn không thể bị huỷ.

#### Parameters

- `tracking_order` (path, required): Mã đơn hàng GHTK hoặc mã Đối tác được truyền qua ở trường orderView.id

#### Response

**Success Examples:**

Huỷ thành công:
```json
{
  "success": true,
  "message": "",
  "log_id": "abc123"
}
```

Đơn hàng đã huỷ:
```json
{
  "success": false,
  "message": "Đơn hàng đã đã ở trạng thái hủy",
  "log_id": "def456"
}
```

Không thể huỷ:
```json
{
  "success": false,
  "message": "Đơn đã lấy hàng, không thể hủy đơn.",
  "log_id": "ghi789"
}
```

---

### 5. In nhãn đơn hàng

**GET** `/shipments/{tracking_order}/label`

Tải về file PDF chứa nhãn đơn hàng theo mã vận đơn. Hỗ trợ 2 kiểu định dạng: khổ ngang (landscape) và khổ dọc (portrait). Kích thước hỗ trợ: A5 hoặc A6 (mặc định là A6).

#### Parameters

- `tracking_order` (path, required): Mã vận đơn GHTK
- `original` (query, optional): Kiểu in nhãn
  - `portrait`: in dọc (mặc định)
  - `landscape`: in ngang
- `paper_size` (query, optional): Kích thước khổ giấy A5 hoặc A6 (mặc định là A6)

#### Response

**Success (200):**
Returns PDF file with headers:
- `Content-Disposition: attachment; filename="label.pdf"`
- `Content-Transfer-Encoding: binary`

**Error Responses:**
- `400`: Lỗi do request không hợp lệ
  ```json
  {
    "success": false,
    "message": "Mã vận đơn không hợp lệ, không tìm thấy vận đơn"
  }
  ```
- `401`: Thiếu hoặc sai thông tin xác thực
- `500`: Lỗi hệ thống

---

## Status Codes

- `200`: Success - Request đã được xử lý thành công
- `400`: Bad Request - Dữ liệu đầu vào không hợp lệ
- `401`: Unauthorized - Thiếu hoặc sai thông tin xác thực
- `404`: Not Found - Không tìm thấy tài nguyên
- `500`: Internal Server Error - Lỗi máy chủ nội bộ

## Security

API sử dụng Token-based authentication. Bao gồm header `Token` trong tất cả các request:

```
Token: YOUR_API_TOKEN
```