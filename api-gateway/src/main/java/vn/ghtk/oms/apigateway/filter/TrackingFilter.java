package vn.ghtk.oms.apigateway.filter;

//import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.UUID;

import static vn.ghtk.frk.tracing.UserContext.CORRELATION_ID;

@Order(1)
@Component
public class TrackingFilter implements GlobalFilter, Ordered {
    private static final Logger log = LoggerFactory.getLogger(TrackingFilter.class);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        var request = exchange.getRequest();
        String correlationId = request.getHeaders().getFirst(CORRELATION_ID);
        if (correlationId == null || correlationId.isEmpty()) {
            correlationId = UUID.randomUUID().toString();
            log.debug("Correlation Id not found, generated: {}", correlationId);
        } else {
            log.debug("Correlation Id found: {}", correlationId);
        }

        var modifiedRequest = request.mutate()
                .header(CORRELATION_ID, correlationId)
                .build();

        String finalCorrelationId = correlationId;
        return chain.filter(exchange.mutate().request(modifiedRequest).build())
                .then(Mono.fromRunnable(() -> {
                    exchange.getResponse().getHeaders().add(CORRELATION_ID, finalCorrelationId);
                }));
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
