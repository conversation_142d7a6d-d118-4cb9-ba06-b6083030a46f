spring:
  main:
    web-application-type: reactive
  application:
    name: api-gateway
  config:
#    import: configserver:http://config-server:8071
    import: configserver:${CONFIG_SERVER_URL:http://localhost:8071}
  profiles:
#    active: kubernetes
    active: test
  cloud:
    gateway:
      routes:
        - id: order-service
          uri: ${ORDER_SERVICE_URL:http://localhost:8081}
          predicates:
            - Path=/api/v1/orders/**
