package vn.ghtk.oms.partner.logistics.operations;

import vn.ghtk.oms.ordermgt.domain.LogisticsPartner;
import vn.ghtk.oms.ordermgt.domain.LogisticsService;
import vn.ghtk.oms.ordermgt.domain.Order;
import vn.ghtk.oms.ordermgt.domain.Shipment;

public class DefaultLogisticsService implements LogisticsService {
    private final LogisticsPartnerSelector logisticsPartnerSelector;
    private final LogisticsGateway gateway;

    public DefaultLogisticsService(LogisticsPartnerSelector logisticsPartnerSelector,
                                   LogisticsGateway gateway,
                                   PartnerEndpointRepository partnerEndpointRepository) {
        this.logisticsPartnerSelector = logisticsPartnerSelector;
        this.gateway = gateway;
    }

    @Override
    public LogisticsPartner selectSuitablePartnerFor(Order order) {
        // TODO
        return this.logisticsPartnerSelector.selectSuitablePartnerFor(order);
    }

    @Override
    public Shipment createShipment(Order order, LogisticsPartner partner) {
        return this.gateway.createShipmentFor(order, partner);
    }
}
