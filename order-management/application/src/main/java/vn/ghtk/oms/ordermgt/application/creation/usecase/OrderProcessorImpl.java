package vn.ghtk.oms.ordermgt.application.creation.usecase;


import vn.ghtk.frk.event.handling.EventPublishHandler;
import vn.ghtk.frk.outbox.application.store.EventStoreProcessor;
import vn.ghtk.oms.ordermgt.application.creation.inbound.OrderCreationCmd;
import vn.ghtk.oms.ordermgt.application.creation.inbound.OrderProcessor;
import vn.ghtk.oms.ordermgt.domain.*;

import java.util.ArrayList;
import java.util.List;

public class OrderProcessorImpl implements OrderProcessor {
    private final OrderRepository orderRepository;
    private final OrderFactory orderFactory;

    public OrderProcessorImpl(OrderRepository orderRepository,  OrderFactory orderFactory) {
        this.orderRepository = orderRepository;
        this.orderFactory = orderFactory;
    }

    @Override
    @EventPublishHandler(eventProcessors = {EventStoreProcessor.class})
    public void createOrder(OrderCreationCmd cmd) {
        Order order = this.orderFactory.create(createOrderCreationParams(cmd));
        this.orderRepository.save(order);
    }

    private OrderFactoryParams createOrderCreationParams(OrderCreationCmd cmd) {
        return new OrderFactoryParams(
                this.createOrderParam(cmd),
                this.createItems(cmd),
                this.createPickup(cmd),
                this.createDelivery(cmd),
                cmd.useReturnAddress(),
                this.createReturn(cmd),
                this.createMetadata(cmd)
        );
    }

    private OrderParams createOrderParam(OrderCreationCmd cmd) {
        return cmd.orderParams();
    }

    private List<Item> createItems(OrderCreationCmd cmd) {
        return cmd.items();
    }

    private Pickup createPickup(OrderCreationCmd cmd) {
        return cmd.pickup();
    }

    private Delivery createDelivery(OrderCreationCmd cmd) {
        return cmd.delivery();
    }

    private Return createReturn(OrderCreationCmd cmd) {
        return cmd.returnInfo();
    }

    private Metadata createMetadata(OrderCreationCmd cmd) {
        return cmd.metadata();
    }

    @Override
    public void cancelOrder() {
        // TODO
    }

    @Override
    public Order findByPartnerOrder(PartnerOrderId partnerOrderId) {
        // TODO
        return null;
    }
}
