package vn.ghtk.oms.ordermgt.application.creation.usecase;

import vn.ghtk.oms.ordermgt.application.creation.inbound.Price;
import vn.ghtk.oms.ordermgt.application.creation.inbound.PriceEstimator;
import vn.ghtk.oms.ordermgt.application.creation.outbound.PricingService;

public class SimplePriceEstimator implements PriceEstimator {
    private final PricingService pricingService;

    public SimplePriceEstimator(PricingService pricingService) {
        this.pricingService = pricingService;
    }

    @Override
    public Price execute() {
        return null;
    }
}
