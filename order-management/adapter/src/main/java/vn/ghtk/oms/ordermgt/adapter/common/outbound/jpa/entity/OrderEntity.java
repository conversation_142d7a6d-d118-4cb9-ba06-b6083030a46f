package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import jakarta.persistence.*;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "orders")
public class OrderEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_number", nullable = false, unique = true)
    private Long orderNumber;

    @Column(name = "partner_order_id", nullable = false, length = 255, unique = true)
    private String partnerOrderId;

    @Column(name = "status", nullable = false)
    private Short status;

    @Column(name = "weight_value", nullable = false, precision = 10, scale = 3)
    private BigDecimal weightValue;

    @Column(name = "weight_unit", nullable = false, length = 10)
    private String weightUnit;

    @Column(name = "note", columnDefinition = "TEXT")
    private String note;

    @Column(name = "cod_amount_value", nullable = false, precision = 15, scale = 2)
    private BigDecimal codAmountValue;

    @Column(name = "cod_amount_currency", nullable = false, length = 3)
    private String codAmountCurrency;

    @Column(name = "is_free_ship", nullable = false)
    private Boolean isFreeShip;

    @Column(name = "declared_value_value", nullable = false, precision = 15, scale = 2)
    private BigDecimal declaredValueValue;

    @Column(name = "declared_value_currency", nullable = false, length = 3)
    private String declaredValueCurrency;

    @Column(name = "is_opm", nullable = false)
    private Boolean isOpm;

    @Column(name = "transport_method", length = 50)
    private String transportMethod;

    @Column(name = "actual_transport_method", nullable = false, length = 50)
    private String actualTransportMethod;

    @Column(name = "created_at", updatable = false, insertable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", updatable = false, insertable = false)
    private LocalDateTime updatedAt;

    @Version
    @Column(name = "version", nullable = false)
    private Integer version;
} 