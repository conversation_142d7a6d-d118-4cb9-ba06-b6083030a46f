package vn.ghtk.oms.ordermgt.adapter.configuration;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import vn.ghtk.frk.springservice.configuration.GMicroserviceConfiguration;
import vn.ghtk.oms.ordermgt.application.creation.inbound.OrderProcessor;
import vn.ghtk.oms.ordermgt.application.creation.usecase.OrderProcessorImpl;
import vn.ghtk.oms.ordermgt.domain.LogisticsService;
import vn.ghtk.oms.ordermgt.domain.OrderFactory;
import vn.ghtk.oms.ordermgt.domain.OrderRepository;

@Configuration
@Import({GMicroserviceConfiguration.class})
public class OrderServiceConfiguration {
    @Bean
    public OrderFactory orderFactory(LogisticsService logisticsService) {
        return new OrderFactory(logisticsService);
    }

    @Bean
    public OrderProcessor orderProcessor(OrderRepository orderRepository, OrderFactory orderFactory) {
        return new OrderProcessorImpl(orderRepository, orderFactory);
    }
}
