package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_tags")
public class OrderTagEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // PK

    @Column(name = "order_number", nullable = false)
    private Long orderNumber; // Mã đơn hàng

    @Column(name = "tag_value", nullable = false, length = 255)
    private String tagValue; // Gi<PERSON> trị thẻ tag

    @Column(name = "tag_type", nullable = false, length = 20)
    private String tagType; // Loại thẻ

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 