package vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeliveryCreationRequest {
    private String name;
    private String address;
    private String province;
    private String district;
    private String ward;
    private String street;
    private String hamlet;
    private String tel;
    private String email;
    private Integer workShift;
    private String date;
    private String method;
}
