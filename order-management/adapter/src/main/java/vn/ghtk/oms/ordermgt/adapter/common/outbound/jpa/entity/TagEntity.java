package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tags")
public class TagEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id; // PK

    @Column(name = "name", nullable = false, length = 255)
    private String name; // Tên tag

    @Column(name = "description", columnDefinition = "TEXT")
    private String description; // Mô tả

    @Column(name = "hash", nullable = false, length = 32, unique = true)
    private String hash; // Hash md5 tên tag

    @Column(name = "type")
    private Short type; // Loại tag

    @Column(name = "group_id")
    private Integer groupId; // Nhóm tag

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 