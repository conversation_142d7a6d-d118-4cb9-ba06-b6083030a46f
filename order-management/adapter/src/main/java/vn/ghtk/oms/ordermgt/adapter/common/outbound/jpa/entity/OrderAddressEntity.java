package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_addresses")
public class OrderAddressEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // PK

    @Column(name = "order_number", nullable = false)
    private Long orderNumber; // Mã đơn hàng

    @Column(name = "address_type", nullable = false, length = 10)
    private String addressType; // Loại địa chỉ

    @Column(name = "contact_name", nullable = false, length = 255)
    private String contactName; // Tên người liên hệ

    @Column(name = "contact_phone", nullable = false, length = 20)
    private String contactPhone; // Số điện thoại liên hệ

    @Column(name = "contact_email", length = 255)
    private String contactEmail; // Email liên hệ

    @Column(name = "address_detail", columnDefinition = "TEXT")
    private String addressDetail; // Địa chỉ chi tiết

    @Column(name = "street", length = 255)
    private String street; // Đường/phố

    @Column(name = "hamlet", length = 255)
    private String hamlet; // Thôn/ấp

    @Column(name = "ward", length = 255)
    private String ward; // Phường/xã

    @Column(name = "district", nullable = false, length = 255)
    private String district; // Quận/huyện

    @Column(name = "province", nullable = false, length = 255)
    private String province; // Tỉnh/thành phố

    @Column(name = "workshift", nullable = false)
    private Integer workshift; // Ca làm việc

    @Column(name = "date")
    private LocalDate date; // Ngày giao/nhận

    @Column(name = "delivery_method", length = 20)
    private String deliveryMethod; // Phương thức giao hàng

    @Column(name = "created_at", updatable = false, insertable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", updatable = false, insertable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 