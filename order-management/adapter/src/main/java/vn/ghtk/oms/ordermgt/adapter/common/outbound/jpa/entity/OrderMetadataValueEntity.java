package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_metadata_values")
public class OrderMetadataValueEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // PK

    @Column(name = "order_number", nullable = false, unique = true)
    private Long orderNumber; // Mã đơn hàng

    @Column(name = "metadata", columnDefinition = "JSON")
    private String metadata; // Dữ liệu metadata (JSON string)

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 