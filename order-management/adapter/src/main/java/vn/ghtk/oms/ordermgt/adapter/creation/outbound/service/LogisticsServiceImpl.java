package vn.ghtk.oms.ordermgt.adapter.creation.outbound.service;

import org.springframework.stereotype.Component;
import vn.ghtk.oms.ordermgt.domain.*;

@Component
public class LogisticsServiceImpl implements LogisticsService {

    @Override
    public LogisticsPartner selectSuitablePartnerFor(Order order) {
        return new LogisticsPartner("ghtk");
    }

    @Override
    public Shipment createShipment(Order order, LogisticsPartner partner) {
        return new Shipment("SHIP123", partner);
    }
}
