package vn.ghtk.oms.ordermgt.adapter.configuration;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@ComponentScan(basePackages = {"vn.ghtk.oms.ordermgt.adapter"})
@EntityScan(basePackages = {"vn.ghtk.oms.ordermgt.adapter"})
@EnableJpaRepositories(basePackages = {"vn.ghtk.oms.ordermgt.adapter"})
public class AdapterConfiguration {

    @Bean
    public CreateProduct createProduct(DomainRepository<Product> productRepository) {
        return new CreateProductUc(productRepository);
    }

    @Bean
    public BrowseProduct browseProduct(DomainRepository<Product> productRepository) {
        return new BrowseProductUc(productRepository);
    }

    @Bean
    public UpdateProductAvailabilityStatus updateProductAvailabilityStatus(DomainRepository<Product> productRepository) {
        return new UpdateProductAvailabilityStatusUc(productRepository);
    }

}
