package vn.ghtk.oms.ordermgt.adapter.creation.inbound;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.ghtk.frk.springservice.web.common.CommonHttpResponse;
import vn.ghtk.oms.ordermgt.adapter.creation.facade.OrderCreationFacade;
import vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto.OrderCreationRequest;
import vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto.OrderView;

@RestController
@RequestMapping("/api/v1/orders")
@RequiredArgsConstructor
public class OrderCreationController {
    private final OrderCreationFacade facade;

    @PostMapping
    public ResponseEntity<CommonHttpResponse> createOrder(@RequestBody OrderCreationRequest request) {
        OrderView orderView = facade.createOrder(request);
        CommonHttpResponse response = new CommonHttpResponse(HttpStatus.CREATED, orderView);
        return ResponseEntity.status(response.getStatus()).body(response);
    }
}
