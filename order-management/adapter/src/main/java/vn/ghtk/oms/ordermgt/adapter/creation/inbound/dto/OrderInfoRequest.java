package vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderInfoRequest {
    private String id;
    private Long codAmount;
    private String note;
    private Integer isFreeship;
    private String weightOption;
    private BigDecimal totalWeight;
    private String labelId;
    private String expired;
    private Long declaredValue;
    private Integer opm;
    private String actualTransferMethod;
    private String transport;
    private List<String> tags;
    private List<String> subTags;
}
