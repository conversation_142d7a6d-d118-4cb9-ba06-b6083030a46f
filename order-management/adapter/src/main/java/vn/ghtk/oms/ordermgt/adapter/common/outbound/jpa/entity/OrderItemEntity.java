package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_items")
public class OrderItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // PK

    @Column(name = "order_number", nullable = false)
    private Long orderNumber; // Mã đơn hàng

    @Column(name = "name", nullable = false, length = 500)
    private String name; // Tên sản phẩm

    @Column(name = "price_value", nullable = false, precision = 15, scale = 2)
    private BigDecimal priceValue; // Giá trị sản phẩm

    @Column(name = "price_currency", nullable = false, length = 3)
    private String priceCurrency; // Đơn vị tiền tệ giá sản phẩm

    @Column(name = "weight_value", nullable = false, precision = 10, scale = 3)
    private BigDecimal weightValue; // Trọng lượng sản phẩm

    @Column(name = "weight_unit", nullable = false, length = 10)
    private String weightUnit; // Đơn vị trọng lượng sản phẩm

    @Column(name = "quantity", nullable = false)
    private Integer quantity; // Số lượng sản phẩm

    @Column(name = "code", nullable = false, length = 255)
    private String code; // Mã sản phẩm/SKU

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 