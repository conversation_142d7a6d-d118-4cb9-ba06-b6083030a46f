package vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity;

import lombok.*;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "order_shipments")
public class OrderShipmentEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // PK

    @Column(name = "order_number", nullable = false, unique = true)
    private Long orderNumber; // Mã đơn hàng

    @Column(name = "shipment_code", nullable = false, length = 255)
    private String shipmentCode; // Mã vận đơn

    @Column(name = "partner_code", nullable = false, length = 255)
    private String partnerCode; // Mã đối tác logistics

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt; // Thời gian tạo bản ghi

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt; // Thời gian cập nhật bản ghi
} 