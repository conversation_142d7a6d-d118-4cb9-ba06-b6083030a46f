package vn.ghtk.oms.ordermgt.adapter.creation.outbound;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.entity.OrderEntity;
import vn.ghtk.oms.ordermgt.adapter.common.outbound.jpa.repository.OrderJpaRepository;
import vn.ghtk.oms.ordermgt.domain.Order;
import vn.ghtk.oms.ordermgt.domain.OrderRepository;

import java.math.BigDecimal;

@Component
@RequiredArgsConstructor
public class OrderOa implements OrderRepository {
    private final OrderJpaRepository orderJpaRepository;

    @Override
    public void save(Order order) {
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setOrderNumber(1L);
        orderEntity.setPartnerOrderId(order.getPartnerOrderId().value());
        orderEntity.setStatus((short) 0);
        orderEntity.setWeightValue(BigDecimal.valueOf(order.getWeight().value()));
        orderEntity.setWeightUnit(order.getWeight().unit().name());
        orderEntity.setNote(order.getNote());
        orderEntity.setCodAmountValue(order.getCodAmount().amount());
        orderEntity.setCodAmountCurrency(order.getCodAmount().currency().code());
        orderEntity.setIsFreeShip(order.getIsFreeShip());
        orderEntity.setDeclaredValueValue(order.getDeclaredValue().amount());
        orderEntity.setDeclaredValueCurrency(order.getDeclaredValue().currency().code());
        orderEntity.setIsOpm(order.getIsOpm());
        orderEntity.setTransportMethod(order.getTransportMethod().name());
        orderEntity.setActualTransportMethod(order.getActualTransportMethod().name());
        orderEntity.setVersion(1);
        orderJpaRepository.save(orderEntity);

    }
}
