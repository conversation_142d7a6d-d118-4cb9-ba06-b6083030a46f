package vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import vn.ghtk.frk.domain.type.Currency;
import vn.ghtk.oms.common.types.*;
import vn.ghtk.oms.ordermgt.application.creation.inbound.OrderCreationCmd;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import vn.ghtk.oms.ordermgt.domain.*;
import vn.ghtk.frk.domain.type.Money;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderCreationRequest {
    private DeliveryCreationRequest delivery;
    private PickupCreationRequest pickup;
    private Integer useReturnAddress;
    @JsonProperty("return")
    private ReturnCreationRequest returnInfo;
    private List<OrderItemRequest> items;
    private OrderInfoRequest order;
    private Map<String, String> metadata;

    public OrderCreationCmd buildOrderCreationCmd() {
        Delivery delivery = new Delivery(
            new ContactInfo(new Name(this.delivery.getName()), new Tel(this.delivery.getTel()), new Email(this.delivery.getEmail())),
            new Workshift(this.delivery.getWorkShift()),
            this.delivery.getDate() != null ? LocalDate.parse(this.delivery.getDate()) : null,
            this.delivery.getMethod() != null ? DeliveryMethod.fromKey(this.delivery.getMethod()) : null,
            new FullAddress(
                new Address(this.delivery.getAddress()),
                new Street(this.delivery.getStreet()),
                new Hamlet(this.delivery.getHamlet()),
                new Ward(this.delivery.getWard()),
                new District(this.delivery.getDistrict()),
                new Province(this.delivery.getProvince())
            )
        );

        Pickup pickup = new Pickup(
            new ContactInfo(new Name(this.pickup.getName()), new Tel(this.pickup.getTel()), new Email(this.pickup.getEmail())),
            new Workshift(this.pickup.getWorkShift()),
            this.pickup.getDate() != null ? LocalDate.parse(this.pickup.getDate()) : null,
            this.pickup.getMethod() != null ? DeliveryMethod.fromKey(this.pickup.getMethod()) : null,
            new FullAddress(
                new Address(this.pickup.getAddress()),
                new Street(this.pickup.getStreet()),
                new Hamlet(this.pickup.getHamlet()),
                new Ward(this.pickup.getWard()),
                new District(this.pickup.getDistrict()),
                new Province(this.pickup.getProvince())
            )
        );

        Return returnInfo = this.returnInfo == null ? null : new Return(
            new ContactInfo(new Name(this.returnInfo.getName()), new Tel(this.returnInfo.getTel()), new Email(this.returnInfo.getEmail())),
            new FullAddress(
                new Address(this.returnInfo.getAddress()),
                new Street(this.returnInfo.getStreet()),
                new Hamlet(this.returnInfo.getHamlet()),
                new Ward(this.returnInfo.getWard()),
                new District(this.returnInfo.getDistrict()),
                new Province(this.returnInfo.getProvince())
            )
        );

        java.util.List<Item> items = this.items == null ? java.util.Collections.emptyList() : this.items.stream().map(i ->
            new Item(
                i.getName(),
                new Money(BigDecimal.valueOf(i.getPrice()), new Currency("VND")),
                new Weight(i.getWeight().doubleValue(), WeightUnit.KILOGRAM),
                i.getQuantity(),
                i.getCode()
            )
        ).toList();

        OrderParams orderParams = this.order == null ? null : new OrderParams(
            new PartnerOrderId(this.order.getId()),
            new Money(BigDecimal.valueOf(this.order.getCodAmount()), new Currency("VND")),
            this.order.getNote(),
            this.order.getIsFreeship() != null && this.order.getIsFreeship() == 1,
            this.order.getTotalWeight() != null ? new Weight(this.order.getTotalWeight().doubleValue(), WeightUnit.KILOGRAM) : null,
            this.order.getExpired() != null ? LocalDate.parse(this.order.getExpired()) : null,
            this.order.getLabelId(),
            this.order.getDeclaredValue() != null ? new Money(BigDecimal.valueOf(this.order.getDeclaredValue()), new Currency("VND")) : null,
            this.order.getOpm() != null && this.order.getOpm() == 1,
            this.order.getTransport() != null ? TransportMethod.fromKey(this.order.getTransport()) : null,
            this.order.getActualTransferMethod() != null ? TransportMethod.fromKey(this.order.getActualTransferMethod()) : null,
            this.order.getTags() != null ? this.order.getTags().stream().map(Tag::new).toList() : Collections.emptyList(),
            this.order.getSubTags() != null ? this.order.getSubTags().stream().map(Tag::new).toList() : Collections.emptyList()
        );

//        Metadata metadata = this.metadata == null ? null : new Metadata(
//                new Instruction(this.metadata.get("instruction"))
//        );

        return new OrderCreationCmd(
            delivery,
            pickup,
            this.useReturnAddress != null && this.useReturnAddress == 1,
            returnInfo,
            orderParams,
            items,
            null
        );
    }

    public String orderId() {
        // TODO
        return null;
    }
}
