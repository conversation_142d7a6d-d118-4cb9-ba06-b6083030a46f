package vn.ghtk.oms.ordermgt.adapter.creation.facade;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.ghtk.frk.springcore.facade.Facade;
import vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto.OrderCreationRequest;
import vn.ghtk.oms.ordermgt.adapter.creation.inbound.dto.OrderView;
import vn.ghtk.oms.ordermgt.application.creation.inbound.OrderProcessor;
import vn.ghtk.oms.ordermgt.domain.Order;
import vn.ghtk.oms.ordermgt.domain.PartnerOrderId;

@Service
@RequiredArgsConstructor
public class OrderCreationFacade implements Facade {
    private final OrderProcessor orderProcessor;
    // private final PriceEstimator priceEstimator;
    // private final ShippingLabelGenerator shippingLabelGenerator;

    @Transactional
    public OrderView createOrder(OrderCreationRequest request) {
        this.orderProcessor.createOrder(request.buildOrderCreationCmd());
        Order createdOrder = this.orderProcessor.findByPartnerOrder(new PartnerOrderId(request.orderId()));
        return OrderView.of(createdOrder);
    }

}
