package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.type.Money;
import vn.ghtk.oms.common.types.TransportMethod;
import vn.ghtk.oms.common.types.Weight;

import java.time.LocalDate;
import java.util.List;

public record OrderParams(
        PartnerOrderId partnerOrderId,
        Money codAmount,
        String note,
        boolean isFreeShip,
        Weight weight,
        LocalDate expired,
        String labelId,
        Money declaredValue,
        boolean isOpm,
        TransportMethod transportMethod,
        TransportMethod actualTransportMethod,
        List<Tag> tags,
        List<Tag> subTags
) {
}
