package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.DomainValue;
import vn.ghtk.frk.domain.exception.InvalidArgumentException;
import vn.ghtk.oms.common.types.*;

public record Return(
        ContactInfo contactInfo,
        FullAddress fullAddress) implements DomainValue {
    public Return {
        if (contactInfo == null) {
            throw new InvalidArgumentException("ContactInfo cannot be null");
        }
        if (fullAddress == null) {
            throw new InvalidArgumentException("FullAddress cannot be null");
        }
    }
}
