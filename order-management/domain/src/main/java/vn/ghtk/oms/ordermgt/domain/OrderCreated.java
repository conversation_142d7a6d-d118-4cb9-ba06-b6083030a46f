package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.ddd.AbstractDomainEvent;
import vn.ghtk.frk.domain.ddd.DomainEventType;
import vn.ghtk.frk.domain.type.DTime;

import static vn.ghtk.oms.ordermgt.domain.OrderManagementConstants.BOUNDED_CONTEXT_NAME;

public class OrderCreated extends AbstractDomainEvent {
    private final Order order;

    public OrderCreated(Order order, DTime occurredAt) {
        super(DomainEventType.of(BOUNDED_CONTEXT_NAME, "OrderCreated"),
                occurredAt);
        this.order = order;
        this.subject = this.order.id().toString();
    }

    public Order order() {
        return order;
    }
}
