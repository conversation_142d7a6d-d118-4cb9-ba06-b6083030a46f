package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.oms.common.types.*;

import java.time.LocalDate;

public class Pickup extends CommonDeliveryInfo {
    private String addressId;

    public Pickup(ContactInfo contactInfo,
                  Workshift workshift,
                  LocalDate date,
                  DeliveryMethod method,
                  FullAddress fullAddress) {
        super(contactInfo, workshift, date, method, fullAddress);
    }
}
