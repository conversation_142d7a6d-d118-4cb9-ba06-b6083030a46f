package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.oms.common.types.*;

import java.time.LocalDate;

public class Delivery extends CommonDeliveryInfo {

    public Delivery(ContactInfo contactInfo,
                    Workshift workshift,
                    LocalDate date,
                    DeliveryMethod method,
                    FullAddress fullAddress) {
        super(contactInfo, workshift, date, method, fullAddress);
        if (fullAddress.hamlet() == null) {
            throw new IllegalArgumentException("Hamlet is required");
        }
    }

}
