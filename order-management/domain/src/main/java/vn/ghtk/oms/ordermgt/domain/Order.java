package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.DomainRegistry;
import vn.ghtk.frk.domain.ddd.Aggregate;
import vn.ghtk.frk.domain.type.Money;
import vn.ghtk.oms.common.types.TransportMethod;
import vn.ghtk.oms.common.types.Weight;

import java.time.LocalDate;
import java.util.List;

public class Order extends Aggregate<OrderId> {

    private PartnerOrderId partnerOrderId;
    private Weight weight;
    private String note;
    private Money codAmount;
    private boolean isFreeShip;
    private String labelId;
    private LocalDate expired;
    private Money declaredValue;
    private boolean isOpm;
    private TransportMethod transportMethod;
    private TransportMethod actualTransportMethod;
    private List<Tag> tags;
    private List<Tag> subTags;
    private List<Item> items;

    private Pickup pickup;
    private Delivery delivery;
    private Return returnInfo;

    private Metadata metadata;

    private Shipment shipment;

    public Order(PartnerOrderId partnerOrderId,
                 Weight weight,
                 String note,
                 Money codAmount,
                 boolean isFreeShip,
                 String labelId,
                 LocalDate expired,
                 Money declaredValue,
                 boolean isOpm,
                 TransportMethod transportMethod,
                 TransportMethod actualTransportMethod,
                 List<Tag> tags,
                 List<Tag> subTags,
                 List<Item> items,
                 Pickup pickup,
                 Delivery delivery,
                 Return returnInfo,
                 Metadata metadata) {
        this.id = null;
        this.partnerOrderId = partnerOrderId;
        this.weight = weight;
        this.note = note;
        this.codAmount = codAmount;
        this.isFreeShip = isFreeShip;
        this.labelId = labelId;
        this.expired = expired;
        this.declaredValue = declaredValue;
        this.isOpm = isOpm;
        this.transportMethod = transportMethod;
        this.actualTransportMethod = actualTransportMethod;
        this.tags = tags;
        this.subTags = subTags;
        this.items = items;
        this.pickup = pickup;
        this.delivery = delivery;
        this.returnInfo = returnInfo;
        this.metadata = metadata;
    }

    ShippingLabel printShippingLabel() {
        // TODO
        return null;
    }

    // Getters and Setters
    public PartnerOrderId getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(PartnerOrderId partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

    public Weight getWeight() {
        return weight;
    }

    public void setWeight(Weight weight) {
        this.weight = weight;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Money getCodAmount() {
        return codAmount;
    }

    public void setCodAmount(Money codAmount) {
        this.codAmount = codAmount;
    }

    public boolean getIsFreeShip() {
        return isFreeShip;
    }

    public void setFreeShip(boolean freeShip) {
        isFreeShip = freeShip;
    }

    public String getLabelId() {
        return labelId;
    }

    public void setLabelId(String labelId) {
        this.labelId = labelId;
    }

    public LocalDate getExpired() {
        return expired;
    }

    public void setExpired(LocalDate expired) {
        this.expired = expired;
    }

    public Money getDeclaredValue() {
        return declaredValue;
    }

    public void setDeclaredValue(Money declaredValue) {
        this.declaredValue = declaredValue;
    }

    public boolean getIsOpm() {
        return isOpm;
    }

    public void setOpm(boolean opm) {
        isOpm = opm;
    }

    public TransportMethod getTransportMethod() {
        return transportMethod;
    }

    public void setTransportMethod(TransportMethod transportMethod) {
        this.transportMethod = transportMethod;
    }

    public TransportMethod getActualTransportMethod() {
        return actualTransportMethod;
    }

    public void setActualTransportMethod(TransportMethod actualTransportMethod) {
        this.actualTransportMethod = actualTransportMethod;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public List<Tag> getSubTags() {
        return subTags;
    }

    public void setSubTags(List<Tag> subTags) {
        this.subTags = subTags;
    }

    public List<Item> getItems() {
        return items;
    }

    public void setItems(List<Item> items) {
        this.items = items;
    }

    public Pickup getPickup() {
        return pickup;
    }

    public void setPickup(Pickup pickup) {
        this.pickup = pickup;
    }

    public Delivery getDelivery() {
        return delivery;
    }

    public void setDelivery(Delivery delivery) {
        this.delivery = delivery;
    }

    public Return getReturnInfo() {
        return returnInfo;
    }

    public void setReturnInfo(Return returnInfo) {
        this.returnInfo = returnInfo;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public void setMetadata(Metadata metadata) {
        this.metadata = metadata;
    }

    public Shipment getShipment() {
        return shipment;
    }

    public void setShipment(Shipment shipment) {
        this.shipment = shipment;
    }
}
