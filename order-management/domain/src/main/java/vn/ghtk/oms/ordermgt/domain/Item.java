package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.ddd.PersistentValue;
import vn.ghtk.frk.domain.type.Money;
import vn.ghtk.oms.common.types.Weight;

import java.security.InvalidParameterException;

public class Item extends PersistentValue<Long> {
    private String name;
    private Money price;
    private Weight weight;
    private int quantity;
    private String code;

    public Item(String name,
                Money price,
                Weight weight,
                int quantity,
                String code) {
        setName(name);
        setPrice(price);
        setWeight(weight);
        setQuantity(quantity);
        setCode(code);
    }

    private void setName(String name) {
        if (name == null) {
            throw new InvalidParameterException("Item's name is null");
        }
        this.name = name;
    }

    private void setPrice(Money price) {
        if (price == null) {
            throw new InvalidParameterException("Item's price is null");
        }
        this.price = price;
    }

    private void setWeight(Weight weight) {
        if (weight == null) {
            throw new InvalidParameterException("Item's weight is null");
        }
        this.weight = weight;
    }

    private void setQuantity(int quantity) {
        if (quantity <= 0) {
            throw new InvalidParameterException("Item's quantity must be greater than zero");
        }
        this.quantity = quantity;
    }

    private void setCode(String code) {
        if (code == null) {
            throw new InvalidParameterException("Item's code is null");
        }
        this.code = code;
    }
}
