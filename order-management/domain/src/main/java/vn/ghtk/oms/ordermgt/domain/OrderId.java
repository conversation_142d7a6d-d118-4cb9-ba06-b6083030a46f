package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.ddd.Identity;

import java.util.Objects;

public class OrderId extends Identity<Long> {
    private final Long value;

    public OrderId(Long value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        OrderId orderId = (OrderId) o;
        return Objects.equals(value, orderId.value);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(value);
    }

    @Override
    public Long value() {
        return this.value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }
}
