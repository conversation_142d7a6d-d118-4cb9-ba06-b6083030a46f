package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.DomainValue;
import vn.ghtk.oms.common.types.*;

import java.time.LocalDate;
import java.util.Objects;

public class CommonDeliveryInfo implements DomainValue {

    private final ContactInfo contactInfo;
    private final Workshift workshift;
    private final LocalDate date;
    private final DeliveryMethod method;
    private final FullAddress fullAddress;

    public CommonDeliveryInfo(ContactInfo contactInfo,
                              Workshift workshift,
                              LocalDate date,
                              DeliveryMethod method,
                              FullAddress fullAddress) {
        if (contactInfo == null) {
            throw new IllegalArgumentException("ContactInfo cannot be null");
        }
        if (workshift == null) {
            throw new IllegalArgumentException("Workshift cannot be null");
        }
        if (date == null) {
            throw new IllegalArgumentException("Date cannot be null");
        }
        if (method == null) {
            throw new IllegalArgumentException("DeliveryMethod cannot be null");
        }

        this.contactInfo = contactInfo;
        this.workshift = workshift;
        this.date = date;
        this.method = method;
        this.fullAddress = fullAddress;
    }

    public ContactInfo contactInfo() {
        return contactInfo;
    }

    public Workshift workshift() {
        return workshift;
    }

    public LocalDate date() {
        return date;
    }

    public DeliveryMethod method() {
        return method;
    }

    public FullAddress addressFull() {
        return fullAddress;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommonDeliveryInfo that = (CommonDeliveryInfo) o;
        return Objects.equals(contactInfo, that.contactInfo) &&
               Objects.equals(workshift, that.workshift) &&
               Objects.equals(date, that.date) &&
               method == that.method &&
                Objects.equals(fullAddress, that.fullAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contactInfo, workshift, date, method, fullAddress);
    }

    @Override
    public String toString() {
        return "CommonDeliveryInfo{" +
               "contactInfo=" + contactInfo +
               ", workshift=" + workshift +
               ", date=" + date +
               ", method=" + method +
               ", addressFull=" + fullAddress +
               '}';
    }

}
