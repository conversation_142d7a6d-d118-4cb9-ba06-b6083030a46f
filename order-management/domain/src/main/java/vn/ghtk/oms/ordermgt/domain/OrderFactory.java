package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.ddd.DomainEventPublisher;
import vn.ghtk.frk.domain.ddd.Factory;
import vn.ghtk.frk.domain.exception.ResourceNotFoundException;
import vn.ghtk.frk.domain.type.DTime;

import java.util.List;

public class OrderFactory implements Factory<Order, OrderFactoryParams> {

    private final LogisticsService logisticsService;

    public OrderFactory(LogisticsService logisticsService) {
        this.logisticsService = logisticsService;
    }

    public Order create(OrderFactoryParams params) {

        Order order = createInternalOrder(params);
        LogisticsPartner partner = this.logisticsService.selectSuitablePartnerFor(order);
        if (partner == null) {
            throw new ResourceNotFoundException("Partner not found");
        }

        Shipment shipment = logisticsService.createShipment(order, partner);
        order.setShipment(shipment);

//        DomainEventPublisher.publish(new OrderCreated(
//                order,
//                DTime.now()
//        ));

        return order;
    }

    private Order createInternalOrder(OrderFactoryParams params) {
        // TODO: Validate logic tạo order ở đây
        Return returnInfo = null;
        if (params.useReturnInfo()) {
            returnInfo = params.returnInfo();
        }
        // Kiểm tra logic tag và subtags?
        validateTags(params.orderParams().tags(), params.orderParams().subTags());
        return new Order(params.orderParams().partnerOrderId(),
                params.orderParams().weight(),
                params.orderParams().note(),
                params.orderParams().codAmount(),
                params.orderParams().isFreeShip(),
                params.orderParams().labelId(),
                params.orderParams().expired(),
                params.orderParams().declaredValue(),
                params.orderParams().isOpm(),
                params.orderParams().transportMethod(),
                params.orderParams().actualTransportMethod(),
                params.orderParams().tags(),
                params.orderParams().subTags(),
                params.items(),
                params.pickup(),
                params.delivery(),
                returnInfo,
                params.metadata());
    }

    private void validateTags(List<Tag> tags, List<Tag> subTags) {
        // TODO
    }
}
