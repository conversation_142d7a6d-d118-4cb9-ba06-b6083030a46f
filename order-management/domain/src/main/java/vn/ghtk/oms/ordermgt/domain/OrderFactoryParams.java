package vn.ghtk.oms.ordermgt.domain;

import vn.ghtk.frk.domain.ddd.FactoryParams;
import vn.ghtk.frk.domain.exception.InvalidArgumentException;

import java.util.List;

public record OrderFactoryParams(
        OrderParams orderParams,
        List<Item> items,
        Pickup pickup,
        Delivery delivery,
        boolean useReturnInfo,
        Return returnInfo,
        Metadata metadata
) implements FactoryParams {
    public OrderFactoryParams {
        if (orderParams == null) {
            throw new InvalidArgumentException("OrderParams is null");
        }
        if (items == null || items.isEmpty()) {
            throw new InvalidArgumentException("ItemsParams is null or empty");
        }
        if (pickup == null) {
            throw new InvalidArgumentException("Pickup is null");
        }
        if (delivery == null) {
            throw new InvalidArgumentException("Delivery is null");
        }
        if (this.useReturnInfo() && this.returnInfo() == null) {
            throw new InvalidArgumentException("Return Info must not be null");
        }
    }
}
