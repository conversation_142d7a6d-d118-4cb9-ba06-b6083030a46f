<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>vn.ghtk</groupId>
        <artifactId>oms</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <groupId>vn.ghtk.oms.ordermgt</groupId>
    <artifactId>ordermgt-domain</artifactId>
    <packaging>jar</packaging>

    <name>Order Management Domain</name>

    <dependencies>
        <dependency>
            <groupId>vn.ghtk.frk</groupId>
            <artifactId>domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>vn.ghtk.oms</groupId>
            <artifactId>shared-data</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
