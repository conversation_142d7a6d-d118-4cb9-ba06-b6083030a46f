# OMS (Order Management System)

## Tổng quan dự án

OMS là một hệ thống quản lý đơn hàng được xây dựng theo kiến trúc microservices sử dụng Java 17 và Maven. Dự án được thiết kế để xử lý các quy trình quản lý đơn hàng, logistics, đối tác và định giá một cách hiệu quả và có thể mở rộng.

## Cấu trúc dự án

### Thư mục gốc
```
oms/
├── .git/                           # Git repository
├── .gitignore                      # Git ignore file
├── .idea/                          # IntelliJ IDEA configuration
├── .mvn/                           # Maven wrapper configuration
├── pom.xml                         # Maven parent POM file
└── src/                            # Source code chung
```

### Microservices chính

#### 1. **api-gateway**
- **Mục đích**: C<PERSON>ng <PERSON> ch<PERSON>, điều hướng và xử lý các request từ client
- **Công nghệ**: Spring Boot
- **Dockerfile**: <PERSON><PERSON> sẵn để containerization

#### 2. **config-server**
- **<PERSON><PERSON><PERSON> đí<PERSON>**: Máy chủ cấu hình tập trung cho các microservices
- **Chức năng**: Quản lý cấu hình của toàn bộ hệ thống

#### 3. **orderView-management**
- **Mục đích**: Quản lý đơn hàng
- **Kiến trúc**: Clean Architecture với 3 layer:
  - `domain/`: Domain logic và business rules
  - `application/`: Application services và use cases
  - `adapter/`: Infrastructure và external interfaces

#### 4. **logistics-operations**
- **Mục đích**: Quản lý các hoạt động logistics
- **Chức năng**: Xử lý vận chuyển, kho bãi và các quy trình logistics

#### 5. **logistics-partner-management**
- **Mục đích**: Quản lý đối tác logistics
- **Chức năng**: Quản lý thông tin và tích hợp với các đối tác vận chuyển

#### 6. **logistics-partner-selection**
- **Mục đích**: Lựa chọn đối tác logistics
- **Chức năng**: Thuật toán chọn đối tác tối ưu cho từng đơn hàng

#### 7. **logistics-gateway**
- **Mục đích**: Gateway cho các dịch vụ logistics
- **Chức năng**: Điều phối và tích hợp các dịch vụ logistics

#### 8. **partner-management**
- **Mục đích**: Quản lý đối tác chung
- **Chức năng**: Quản lý thông tin đối tác, merchant

#### 9. **pricing**
- **Mục đích**: Hệ thống định giá
- **Chức năng**: Tính toán giá cước, phí dịch vụ

#### 10. **ticket-management**
- **Mục đích**: Quản lý ticket/khiếu nại
- **Chức năng**: Xử lý các vấn đề, khiếu nại từ khách hàng

#### 11. **webhook-receiver**
- **Mục đích**: Nhận webhook từ các hệ thống bên ngoài
- **Chức năng**: Xử lý callback và notification từ third-party

#### 12. **event-schemas**
- **Mục đích**: Định nghĩa schema cho events
- **Chức năng**: Quản lý cấu trúc dữ liệu cho event-driven architecture

### Backend Support

#### **oms-be**
- **Mục đích**: Backend services hỗ trợ
- **Chức năng**: Các dịch vụ backend bổ sung

## Công nghệ sử dụng

- **Java**: Version 17
- **Build Tool**: Maven
- **Framework**: Spring Boot (dự đoán từ cấu trúc)
- **Architecture Pattern**: Microservices, Clean Architecture
- **Event Streaming**: Kafka (dựa trên Confluent repository)
- **Containerization**: Docker

## Cấu trúc Maven

Dự án sử dụng Maven multi-module với:
- **Parent POM**: `pom.xml` ở thư mục gốc
- **Child Modules**: Mỗi microservice là một module riêng biệt
- **Dependencies**: Quản lý tập trung qua parent POM

## Repositories

- **Maven Central**: https://repo.maven.apache.org/maven2
- **Confluent**: https://packages.confluent.io/maven/ (cho Kafka)

## Kiến trúc tổng thể

```
Client Request
      ↓
  API Gateway
      ↓
┌─────────────────────────────────────────┐
│           Microservices                 │
├─────────────────────────────────────────┤
│ Order Management │ Logistics Operations │
│ Partner Mgmt     │ Pricing             │
│ Ticket Mgmt      │ Webhook Receiver    │
└─────────────────────────────────────────┘
      ↓
  Config Server (Centralized Configuration)
```

## Hướng dẫn phát triển

```sh
mvn -f /path/to/ghtk-frameowork/pom.xml clean install
mvn -f config-server/pom.xml clean spring-boot:run
mvn -f api-gateway/pom.xml spring-boot:run
mvn -f orderView-management/adapter/pom.xml spring-boot:run
```

## Ghi chú

- Dự án tuân theo nguyên tắc microservices với separation of concerns rõ ràng
- Sử dụng event-driven architecture với Kafka
- Clean Architecture được áp dụng cho orderView-management module
- Có hỗ trợ containerization với Docker
