openapi: 3.0.3
servers:
  - url: http://localhost:8080/api/v1
    description: Default server
info:
  title: GHTK Shipment Order API
  version: '1.0'
  description: API để tạo đơn hàng gửi đến GHTK

paths:
  /orders:
    post:
      summary: Tạo đơn hàng giao vận
      tags:
        - Shipment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                delivery:
                  type: object
                  properties:
                    name:
                      type: string
                      description: Tên người nhận hàng
                    address:
                      type: string
                      description: Địa chỉ chi tiết người nhận hàng
                    province:
                      type: string
                      description: Tỉnh/thành phố người nhận
                    district:
                      type: string
                      description: Quận/huyện người nhận
                    ward:
                      type: string
                      description: Phường/xã người nhận (bắt buộc khi không có street)
                    street:
                      type: string
                      description: Tên đường người nhận (bắt buộ<PERSON> khi không có ward)
                    hamlet:
                      type: string
                      description: Thôn/ấp/xóm/tổ... người nhận. <PERSON><PERSON><PERSON> không có, dùng "Khác"
                    tel:
                      type: string
                      description: <PERSON><PERSON> điện thoại người nhận
                    email:
                      type: string
                      format: email
                      description: Email người nhận
                    work_shift:
                      type: integer
                      enum: [1, 2, 3]
                      description: Ca giao hàng (1 = sáng, 2 = chiều, 3 = tối)
                    date:
                      type: string
                      format: date
                      description: Ngày giao hàng (YYYY/MM/DD, yêu cầu cấu hình riêng)
                    method:
                      type: string
                      enum: [xteam]
                      description: Giao hàng bằng phương thức xfast (xteam)

                pickup:
                  type: object
                  required:
                    - name
                    - address
                    - province
                    - district
                    - tel
                  properties:
                    name:
                      type: string
                      description: Tên người liên hệ lấy hàng hóa
                    address_id:
                      type: string
                      description: ID địa điểm lấy hàng từ trang quản lý của khách hàng (ưu tiên nếu có)
                    address:
                      type: string
                      description: Địa chỉ ngắn gọn lấy hàng (VD nhà số 5, tổ 3, ngách 11, ngõ 45)
                    province:
                      type: string
                      description: Tỉnh/thành phố lấy hàng
                    district:
                      type: string
                      description: Quận/huyện lấy hàng
                    ward:
                      type: string
                      description: Phường/xã lấy hàng
                    street:
                      type: string
                      description: Tên đường lấy hàng
                    tel:
                      type: string
                      description: Số điện thoại nơi lấy hàng
                    email:
                      type: string
                      format: email
                      description: Email nơi lấy hàng
                    work_shift:
                      type: integer
                      enum: [1, 2, 3]
                      description: Ca lấy hàng (1 = sáng, 2 = chiều, 3 = tối)
                    date:
                      type: string
                      format: date
                      description: Ngày lấy hàng (YYYY/MM/DD, yêu cầu cấu hình riêng)
                    method:
                      type: string
                      enum: [cod, post]
                      description: Hình thức lấy hàng (COD tới lấy, shop gửi tại bưu cục)

                use_return_address:
                  type: integer
                  enum: [0, 1]
                  description: 1 = sử dụng địa chỉ trả hàng khác; 0 = giống địa chỉ lấy hàng
                return:
                  type: object
                  properties:
                    name:
                      type: string
                      description: Tên người nhận hàng trả
                    address:
                      type: string
                      description: Địa chỉ chi tiết người nhận hàng trả
                    province:
                      type: string
                      description: Tỉnh/thành phố người nhận hàng trả
                    district:
                      type: string
                      description: Quận/huyện người nhận hàng trả
                    ward:
                      type: string
                      description: Phường/xã người nhận hàng trả
                    street:
                      type: string
                      description: Tên đường người nhận hàng trả
                    tel:
                      type: string
                      description: Số điện thoại người nhận hàng trả
                    email:
                      type: string
                      format: email
                      description: Email người nhận hàng trả

                items:
                  type: array
                  items:
                    type: object
                    required: ["name", "weight"]
                    properties:
                      name:
                        type: string
                        description: Tên hàng hóa
                      price:
                        type: integer
                        description: Giá trị hàng hóa
                      weight:
                        type: number
                        format: float
                        description: Khối lượng hàng hóa (kg)
                      quantity:
                        type: integer
                        description: Số lượng hàng hóa
                      code:
                        type: string
                        description: Mã sản phẩm
                
                order:
                  type: object
                  required:
                    - id
                    - declared_value
                  properties:
                    id:
                      type: string
                      description: Mã đơn hàng thuộc hệ thống của đối tác
                    cod_amount:
                      type: integer
                      description: Số tiền COD (VNĐ). Nếu bằng 0 thì không thu tiền
                    note:
                      type: string
                      maxLength: 120
                      description: Ghi chú đơn hàng (tối đa 120 ký tự)
                    is_freeship:
                      type: integer
                      enum: [0, 1]
                      description: 1 = người nhận không chịu phí ship (COD chỉ thu cod_amount)
                    weight_option:
                      type: string
                      enum: [gram, kilogram]
                      default: kilogram
                      description: Đơn vị khối lượng (gram hoặc kilogram)
                    total_weight:
                      type: number
                      format: double
                      description: Tổng khối lượng đơn hàng (sẽ tính theo products.weight nếu không truyền)
                    label_id:
                      type: string
                      description: Mã vận đơn cấp trước (yêu cầu cấu hình riêng)
                    expired:
                      type: string
                      format: date-time
                      description: Thời gian hết hạn đơn hàng (yêu cầu cấu hình riêng)
                    declared_value:
                      type: integer
                      description: Giá trị đóng khai giá, là căn cứ để tính phí khai giá và bồi thường khi có sự cố.
                    opm:
                      type: integer
                      description: 1 = đơn chỉ thu tiền, 0 = bình thường (mặc định)
                    actual_transfer_method:
                      type: string
                      enum: [fly, road]
                      description: Hình thức vận chuyển thực tế (mặc định fly)
                    transport:
                      type: string
                      enum: [fly, road]
                      description: Phương thức vận chuyển
                    tags:
                      type: array
                      items:
                        type: string
                      description: Danh sách nhãn gắn vào đơn hàng
                    sub_tags:
                      type: array
                      items:
                        type: string
                      description: Chi tiết nhãn đơn hàng (bắt buộc khi đơn là hàng cây cối)

                metadata:
                  type: object
                  description: Thông tin bổ sung tùy chỉnh cho đơn hàng
                  additionalProperties: true

              example:
                delivery:
                  name: "Trần Thị B"
                  address: "456 Đường XYZ"
                  province: "Hồ Chí Minh"
                  district: "Quận 1"
                  ward: "Phường Bến Nghé"
                  street: "Đường XYZ"
                  hamlet: "Khác"
                  tel: "0912345678"
                  email: "<EMAIL>"
                  work_shift: 2
                  date: "2025/08/01"
                  method: "xteam"

                pickup:
                  name: "Nguyễn Văn A"
                  address_id: "ADDR001"
                  address: "123 Đường ABC"
                  province: "Hà Nội"
                  district: "Quận Cầu Giấy"
                  ward: "Phường Dịch Vọng"
                  street: "Đường ABC"
                  tel: "0901234567"
                  email: "<EMAIL>"
                  work_shift: 1
                  date: "2025/07/30"
                  method: "cod"

                use_return_address: 1
                return:
                  name: "Phạm Thị C"
                  address: "789 Đường DEF"
                  province: "Đà Nẵng"
                  district: "Quận Hải Châu"
                  ward: "Phường Hòa Cường"
                  street: "Đường DEF"
                  tel: "0923456789"
                  email: "<EMAIL>"

                items:
                  - name: "Áo thun"
                    price: 200000
                    weight: 0.3
                    quantity: 2
                    code: "TSHIRT001"
                  - name: "Quần jeans"
                    price: 400000
                    weight: 0.8
                    quantity: 1
                    code: "JEANS123"

                order:
                  id: "ORDER12345"
                  cod_amount: 100000
                  note: "Giao giờ hành chính"
                  is_freeship: 1
                  weight_option: "kilogram"
                  total_weight: 1.4
                  label_id: "LBL001"
                  expired: "2025-08-10T17:00:00+07:00"
                  declared_value: 500000
                  opm: 0
                  actual_transfer_method: "fly"
                  transport: "road"
                  tags:
                    - "fragile"
                    - "new_customer"
                  sub_tags:
                    - "plant"
                    - "bonsai"

                metadata:
                  delivery_instructions: "Gọi trước 5 phút khi giao"
          
      responses:
        '200':
          description: Đơn hàng đã được tạo thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  order:
                    type: object
                    properties:
                      partner_id:
                        type: string
                      label:
                        type: string
                      area:
                        type: string
                      fee:
                        type: string
                      insurance_fee:
                        type: string
                      tracking_id:
                        type: integer
                      estimated_pickup_time:
                        type: string
                      estimated_delivery_time:
                        type: string
                      items:
                        type: array
                        items:
                          type: object
                      status_id:
                        type: integer
        '400':
          description: Dữ liệu không hợp lệ
        '401':
          description: Không xác thực
        '500':
          description: Lỗi máy chủ nội bộ
      security:
        - ApiKeyAuth: []

  /orders/fee-estimate:
    post:
      summary: Tính phí đơn hàng
      tags:
        - Shipment
      description: |
        API dùng để tính toán phí ship và các phụ phí liên quan đến đơn hàng dựa trên các thông tin như địa chỉ lấy hàng, địa chỉ giao hàng, cân nặng, dịch vụ đơn hàng đã chọn.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                pickup:
                  type: object
                  properties:
                    address_id:
                      type: string
                      description: ID địa điểm lấy hàng của shop (ưu tiên nếu có)
                    address:
                      type: string
                      description: Địa chỉ ngắn gọn lấy hàng
                    province:
                      type: string
                      description: Tỉnh/thành phố nơi lấy hàng hóa
                    district:
                      type: string
                      description: Quận/huyện nơi lấy hàng hóa
                    ward:
                      type: string
                      description: Phường/xã nơi lấy hàng (tùy chọn)
                    street:
                      type: string
                      description: Tên đường/phố nơi lấy hàng (tùy chọn)
                delivery:
                  type: object
                  properties:
                    address:
                      type: string
                      description: Địa chỉ chi tiết của người nhận hàng
                    province:
                      type: string
                      description: Tỉnh/thành phố của người nhận hàng hóa
                    district:
                      type: string
                      description: Quận/huyện của người nhận hàng hóa
                    ward:
                      type: string
                      description: Phường/xã của người nhận hàng hóa (tùy chọn)
                    street:
                      type: string
                      description: Tên đường/phố của người nhận hàng hóa (tùy chọn)
                    method:
                      type: string
                      enum: [xteam, none]
                      description: Phương thức giao hàng. `xteam` nếu sử dụng dịch vụ xfast
                weight:
                  type: integer
                  minimum: 1
                  description: Cân nặng gói hàng (gram)
                value:
                  type: integer
                  minimum: 0
                  description: Giá trị đơn hàng để tính phí khai giá (VNĐ)
                transport:
                  type: string
                  enum: [fly, road]
                  description: Phương thức vận chuyển (`road` - bộ, `fly` - bay)
                tags:
                  type: array
                  items:
                    type: string
                  description: Gắn nhãn cho đơn hàng (dạng mảng)
              example:
                pickup:
                  address_id: "addr_123"
                  address: "123 Lê Lợi"
                  province: "Hà Nội"
                  district: "Hoàn Kiếm"
                  ward: "Hàng Bài"
                  street: "Lê Lợi"
                delivery:
                  address: "456 Nguyễn Huệ"
                  province: "Hồ Chí Minh"
                  district: "Quận 1"
                  ward: "Bến Nghé"
                  street: "Nguyễn Huệ"
                  method: "xteam"
                weight: 1500
                value: 500000
                transport: "road"
                tags: ["fragile", "high-priority"]
      responses:
        '200':
          description: Phí giao hàng được tính toán thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  fee:
                    type: object
                    properties:
                      name:
                        type: string
                        description: Tên gói cước
                      fee:
                        type: integer
                        description: Phí vận chuyển (VNĐ)
                      insurance_fee:
                        type: integer
                        description: Phí bảo hiểm (VNĐ)
                      delivery_type:
                        type: string
                      a:
                        type: integer
                      dt:
                        type: string
                      extFees:
                        type: array
                        items:
                          type: object
                          properties:
                            display:
                              type: string
                            title:
                              type: string
                            amount:
                              type: integer
                            type:
                              type: string
                      delivery:
                        type: boolean
        '400':
          description: Tham số truyền vào không hợp lệ
        '401':
          description: Không có quyền truy cập
        '500':
          description: Lỗi hệ thống
      security:
        - ApiKeyAuth: []

  /orders/{tracking_order}:
    get:
      summary: Lấy trạng thái đơn hàng
      tags:
        - Shipment
      description: |
        API dùng để lấy trạng thái hiện tại của một mã vận đơn GHTK.  
        Dựa trên `order.id` hoặc mã vận đơn GHTK (`label_id`) để truy vấn trạng thái.
      parameters:
        - name: tracking_order
          in: path
          required: true
          description: Mã vận đơn GHTK hoặc mã đơn hàng đối tác
          schema:
            type: string
      responses:
        '200':
          description: Trạng thái đơn hàng được trả về thành công
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: ""
                  order:
                    type: object
                    properties:
                      label_id:
                        type: string
                        description: Mã đơn hàng của hệ thống GHTK
                      partner_id:
                        type: string
                        description: Mã đơn hàng của đối tác
                      status:
                        type: string
                        description: Mã trạng thái đơn hàng
                      status_text:
                        type: string
                        description: Trạng thái đơn hàng
                      created:
                        type: string
                        format: date-time
                      modified:
                        type: string
                        format: date-time
                      message:
                        type: string
                        description: Ghi chú đơn hàng
                      pickup_date:
                        type: string
                        format: date
                        description: Ngày hẹn lấy hàng của đơn hàng nếu có, nếu đơn hàng đã được lấy thành công thì là ngày lấy hàng
                      delivery_date:
                        type: string
                        format: date
                        description: Ngày hẹn giao đơn hàng nếu có, nếu đơn hàng đã được giao hàng thì là ngày giao hàng thành công
                      customer_fullname:
                        type: string
                        description: Họ tên người nhận hàng
                      customer_tel:
                        type: string
                        description: Số điện thoại người nhận hàng
                      address:
                        type: string
                        description: Địa chỉ người nhận hàng
                      storage_day:
                        type: integer
                        description: Số ngày giữ đơn hàng tại kho GHTK trước khi trả hàng
                      shipping_fee:
                        type: integer
                        description: Phí giao hàng
                      insurance:
                        type: integer
                        description: Phí khai giá
                      value:
                        type: integer
                        description: Giá trị đóng khai giá - căn cứ để bồi thường cho người gửi khi có sự cố xảy ra
                      weight:
                        type: integer
                        description: Đơn vị tính gram
                      cod_amount:
                        type: integer
                        description: Số tiền CoD
                      is_freeship:
                        type: boolean
                        description: Miễn phí ship hay không
        '404':
          description: Không tìm thấy đơn hàng
        '401':
          description: Không có quyền truy cập
        '500':
          description: Lỗi máy chủ
      security:
        - ApiKeyAuth: []

    delete:
      summary: Huỷ đơn hàng
      tags:
        - Shipment
      description: |
        API dùng để huỷ 1 đơn hàng đã đẩy lên hệ thống GHTK.

        **TH dùng mã đối tác:**
        `TRACKING_ORDER = partner_id:{PARTNER_CODE}`

        Các trạng thái có thể huỷ:  
        - Trạng thái chưa tiếp nhận (1)  
        - Trạng thái đã tiếp nhận (2)  
        - Trạng thái đang lấy hàng (12)

        Sau trạng thái đã lấy hàng, đơn không thể bị huỷ.
      operationId: cancelShipment
      parameters:
        - name: tracking_order
          in: path
          required: true
          description: Mã đơn hàng GHTK hoặc mã Đối tác được truyền qua ở trường order.id
          schema:
            type: string
      requestBody:
        description: Không yêu cầu body
        required: false
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Phản hồi huỷ đơn
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  log_id:
                    type: string
              examples:
                huy_thanh_cong:
                  summary: Huỷ thành công
                  value:
                    success: true
                    message: ""
                    log_id: "abc123"
                don_da_huy:
                  summary: Đơn hàng đã huỷ
                  value:
                    success: false
                    message: "Đơn hàng đã đã ở trạng thái hủy"
                    log_id: "def456"
                khong_the_huy:
                  summary: Không thể huỷ
                  value:
                    success: false
                    message: "Đơn đã lấy hàng, không thể hủy đơn."
                    log_id: "ghi789"
      security:
        - TokenAuth: []

  /orders/{tracking_order}/label:
    get:
      summary: In nhãn đơn hàng
      tags:
        - Shipment
      description: |
        Tải về file PDF chứa nhãn đơn hàng theo mã vận đơn.  
        Hỗ trợ 2 kiểu định dạng: khổ ngang (landscape) và khổ dọc (portrait).  
        Kích thước hỗ trợ: A5 hoặc A6 (mặc định là A6).
      parameters:
        - name: tracking_order
          in: path
          required: true
          description: Mã vận đơn GHTK
          schema:
            type: string
        - name: original
          in: query
          required: false
          description: |
            Kiểu in nhãn:  
            - `portrait`: in dọc (mặc định)  
            - `landscape`: in ngang
          schema:
            type: string
            enum: [portrait, landscape]
            default: portrait
        - name: paper_size
          in: query
          required: false
          description: Kích thước khổ giấy A5 hoặc A6 (mặc định là A6)
          schema:
            type: string
            enum: [A5, A6]
            default: A6
      responses:
        '200':
          description: Trả về file PDF chứa nhãn đơn hàng
          headers:
            Content-Disposition:
              description: Tên file PDF trả về
              schema:
                type: string
                example: attachment; filename="label.pdf"
            Content-Transfer-Encoding:
              description: Phương thức mã hóa nội dung
              schema:
                type: string
                example: binary
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          description: Lỗi do request không hợp lệ
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Mã vận đơn không hợp lệ, không tìm thấy vận đơn
        '401':
          description: Thiếu hoặc sai thông tin xác thực
        '500':
          description: Lỗi hệ thống
      security:
        - ApiTokenAuth: []

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Token
