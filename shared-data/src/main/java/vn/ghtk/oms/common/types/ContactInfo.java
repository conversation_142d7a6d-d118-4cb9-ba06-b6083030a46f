package vn.ghtk.oms.common.types;

import vn.ghtk.frk.domain.DomainValue;

import java.util.Objects;

public class ContactInfo implements DomainValue {
    private final Name name;
    private final Tel tel;
    private final Email email;


    public ContactInfo(Name name, Tel tel, Email email) {
        this.name = name;
        this.tel = tel;
        this.email = email;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContactInfo that = (ContactInfo) o;

        boolean telEquals = Objects.equals(this.tel, that.tel);
        boolean emailEquals = Objects.equals(this.email, that.email);

        return telEquals || emailEquals;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, tel);
    }

    public Name name() {
        return name;
    }

    public Tel tel() {
        return tel;
    }

    public Email email() {
        return email;
    }
}
