package vn.ghtk.oms.common.types;


public enum DeliveryMethod {
    XTEAM("xteam"),
    COD("cod"),
    ;

    private final String key;

    DeliveryMethod(String key) {
        this.key = key;
    }

    public static DeliveryMethod fromKey(String key) {
        if (key == null) {
            return null;
        }
        for (DeliveryMethod deliveryMethod : DeliveryMethod.values()) {
            if (deliveryMethod.key.equals(key)) {
                return deliveryMethod;
            }
        }
        return null;
    }
}
