package vn.ghtk.oms.common.types;

import vn.ghtk.frk.domain.DomainValue;
import vn.ghtk.frk.domain.exception.InvalidArgumentException;

public record FullAddress(Address address,
                          Street street,
                          Hamlet hamlet,
                          Ward ward,
                          District district,
                          Province province) implements DomainValue {
    public FullAddress {
        if (address == null) {
            throw new InvalidArgumentException("Address cannot be null");
        }

        if (street == null) {
            throw new InvalidArgumentException("Street cannot be null");
        }

        if (ward == null) {
            throw new InvalidArgumentException("Ward cannot be null");
        }

        if (district == null) {
            throw new InvalidArgumentException("District cannot be null");
        }

        if (province == null) {
            throw new InvalidArgumentException("Province cannot be null");
        }
    }
}
