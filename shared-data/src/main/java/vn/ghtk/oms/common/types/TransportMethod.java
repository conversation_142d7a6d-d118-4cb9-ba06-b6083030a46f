package vn.ghtk.oms.common.types;

public enum TransportMethod {
    FLY("fly"),
    ROAD("road"),
    ;

    private final String key;

    TransportMethod(String key) {
        this.key = key;
    }

    public static TransportMethod fromKey(String key) {
        if (key == null) {
            return null;
        }
        for (TransportMethod transportMethod : TransportMethod.values()) {
            if (transportMethod.key.equals(key)) {
                return transportMethod;
            }
        }
        return null;
    }
}
