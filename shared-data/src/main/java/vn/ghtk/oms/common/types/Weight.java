package vn.ghtk.oms.common.types;

import vn.ghtk.frk.domain.exception.InvalidArgumentException;

public record Weight(double value, WeightUnit unit) {
    public Weight {
        if (value < 0) {
            throw new InvalidArgumentException("Weight value cannot be negative.");
        }
        if (unit == null) {
            throw new InvalidArgumentException("Weight unit cannot be null.");
        }
    }

    public Weight convertTo(WeightUnit targetUnit) {
        if (this.unit.equals(targetUnit)) {
            return this;
        }
        double valueInGrams = switch (this.unit) {
            case KILOGRAM -> this.value * 1000;
            case GRAM -> this.value;
        };

        double convertedValue = switch (targetUnit) {
            case KILOGRAM -> valueInGrams / 1000;
            case GRAM -> valueInGrams;
        };

        return new Weight(convertedValue, targetUnit);
    }
}
