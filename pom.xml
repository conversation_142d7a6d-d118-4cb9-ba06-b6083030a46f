<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>vn.ghtk</groupId>
  <artifactId>oms</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>OMS</name>
  <modules>
    <module>partner-management</module>
    <module>logistics-partner-management</module>
    <module>order-management</module>
    <module>logistics-operations</module>
    <module>pricing</module>
    <module>logistics-partner-selection</module>
    <module>ticket-management</module>
    <module>api-gateway</module>
    <module>config-server</module>
    <module>event-schemas</module>
    <module>order-management/domain</module>
    <module>order-management/application</module>
    <module>order-management/adapter</module>
    <module>logistics-gateway</module>
    <module>webhook-receiver</module>
    <module>shared-data</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>21</java.version>

    <order-management.version>1.0.0-SNAPSHOT</order-management.version>
    <shared-date.version>1.0.0-SNAPSHOT</shared-date.version>
    <logistics-operation.version>1.0.0-SNAPSHOT</logistics-operation.version>

    <junit-jupiter.version>5.12.2</junit-jupiter.version>
    <mockito.version>5.17.0</mockito.version>
    <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
    <ghtk-framework.version>1.0.0-RC1</ghtk-framework.version>
  </properties>

  <repositories>
    <repository>
      <id>maven-private</id>
      <url>https://nexus.ghtk.vn/repository/mvn-private</url>
    </repository>
    <repository>
      <id>central</id>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
    <repository>
      <id>confluent</id>
      <url>https://packages.confluent.io/maven/</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>vn.ghtk.frk</groupId>
        <artifactId>release-bom</artifactId>
        <version>${ghtk-framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>vn.ghtk.oms.ordermgt</groupId>
        <artifactId>ordermgt-domain</artifactId>
        <version>${order-management.version}</version>
      </dependency>
      <dependency>
        <groupId>vn.ghtk.oms.ordermgt</groupId>
        <artifactId>ordermgt-application</artifactId>
        <version>${order-management.version}</version>
      </dependency>
      <dependency>
        <groupId>vn.ghtk.oms</groupId>
        <artifactId>shared-data</artifactId>
        <version>${shared-date.version}</version>
      </dependency>
      <dependency>
        <groupId>vn.ghtk.oms</groupId>
        <artifactId>logistics-operations</artifactId>
        <version>${logistics-operation.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>2023.0.1</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit-jupiter.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
