server:
  port: ${CONFIG_SERVER_PORT:8071}
spring:
  application:
    name: config-server
  profiles:
#    active: vault
    active: native
  cloud:
    config:
      server:
        native:
          search-locations: classpath:/config
      fail-fast: false
      retry:
        max-attempts: 5
        initial-interval: 1000
        max-interval: 2000
        multiplier: 1.1
#        vault:
#          host: 127.0.0.1
#          port: 8200
#          scheme: http
#          authentication: token
#          token: myroot
#          kv-version: 2
#          backend: secret
