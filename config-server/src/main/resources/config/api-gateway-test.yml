logging:
  level:
    vn.ghtk: DEBUG
    org.springframework.cloud.gateway: DEBUG

spring:
  cloud:
    kubernetes:
      enabled: false
    gateway:
      discovery:
        locator:
          enabled: false
      routes:
        - id: order-service
          uri: ${ORDER_SERVICE_URL:http://localhost:8081}
          predicates:
            - Path=/api/v1/orders/**
server:
  port: 8080
management:
  endpoints:
    web:
      exposure:
        include: health, info, gateway