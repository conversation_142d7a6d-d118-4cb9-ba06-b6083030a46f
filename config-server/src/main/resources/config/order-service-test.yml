logging:
  level:
    vn.ghtk: DEBUG
    org.springframework.cloud.gateway: DEBUG
server:
  port: 8081
management:
  endpoints:
    web:
      exposure:
        include: health, info, gateway

spring:
  datasource:
#    url: **************************************
#    username: postgres
#    password: "1"
#    driver-class-name: org.postgresql.Driver
    url: jdbc:mysql://${OMS_DATASOURCE_HOST}:${OMS_DATASOURCE_PORT}/${OMS_DATASOURCE_DB}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&connectTimeout=5000&socketTimeout=30000
    username: ${OMS_DATASOURCE_USERNAME}
    password: ${OMS_DATASOURCE_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    schema-registry-url: ${KAFKA_SCHEMA_REGISTRY_URL}

app:
  config:
    event:
      cloudevent:
        specversion: "1.0"
        contenttype:
          avro: "application/avro"

kafka:
  events:
    - source: "source-A"
      type: "event.type.A"
      topic: "topic-a"
      format: "JSON"
    - source: "source-B"
      type: "event.type.B"
      topic: "topic-b"
      format: "JSON"